# Sync/Backup System Investigation Plan

## 1. Key Files to Examine

### Primary Files
- `electron/main/api/sync-logic/unified-sync-engine.ts` - Core sync logic
- `electron/main/api/sync-logic/change-detector.ts` - Detects changes for sync
- `electron/main/api/backup-storage.ts` - Handles backup file operations
- `electron/main/api/backup-engine.ts` - Orchestrates backup process
- `electron/main/api/sync-logic/manifest-manager.ts` - Manages sync manifest

### Secondary Files
- `electron/main/api/sync-logic/file-operations.ts` - File system operations
- `electron/main/database/database.ts` - Database operations
- `electron/main/api/sync-logic/auto-sync.ts` - Auto-sync trigger system

## 2. Potential Inconsistencies to Look For

### Deletion Tracking
- The backup system may be purely additive - only detecting and backing up changes (creates/updates) but never removing items
- Check if `getDeletedItems()` implementation is complete in `change-detector.ts`
- Verify if `removeDeletedItems()` in `backup-storage.ts` is properly called
- Check if deletion events are properly tracked in database

### Rename Handling
- The sync system may use name-based identity matching instead of ID-based matching
- Verify ID-based existence checks are implemented and working
- Check if rename tracking arrays are properly populated
- Examine cleanup system for renamed items

### Book Import Issues
- Check path building logic in `unified-sync-engine.ts`
- Verify cover file handling in import/export process
- Examine book metadata storage in manifest vs. separate files
- Look for duplicate folder creation issues (Books export to root level AND inside Books folder)

## 3. Key Functions to Investigate

### Deletion Handling
- `markForDeletion()` in `change-detector.ts`
- `getDeletedItems()` in `change-detector.ts`
- `removeDeletedItems()` in `backup-storage.ts`
- `performBackup()` in `backup-engine.ts`

### Rename Handling
- `importBook()`, `importFolder()`, `importNote()` in `unified-sync-engine.ts`
- `bookExistsById()`, `folderExistsById()`, `noteExistsById()` in `unified-sync-engine.ts`
- `cleanupRenamedItems()` in `unified-sync-engine.ts`

### Book Import
- `exportBook()` in `unified-sync-engine.ts`
- `buildBookPath()` or similar path construction methods
- Cover file handling in `file-operations.ts`

## 4. Architectural Issues to Investigate

### Change Detection System
- Is the change detection system comprehensive (creates, updates, deletes)?
- Does it properly handle renames as updates rather than create+delete?
- Is the backup directory scanning system working correctly?
- Check if `getDeletedItems()` is just a placeholder returning empty array

### Sync Order and Dependencies
- Is the export order correct (books → folders → notes)?
- Are parent-child relationships properly maintained during import/export?
- Is there proper error handling for orphaned items?

### Manifest and Metadata Management
- Is all metadata consistently stored in the manifest?
- Are IDs properly formatted and consistent between database and manifest?
- Is the manifest properly updated after operations?
- Check if `.book-meta.json` files are created but not tracked in manifest

## 5. Diagnostic Steps

1. **Trace Deletion Flow**:
   - Delete an item in the app
   - Check if deletion is tracked in database
   - Verify if `getDeletedItems()` finds the deleted item
   - Confirm if `removeDeletedItems()` is called with correct items

2. **Trace Rename Flow**:
   - Rename an item in the app
   - Check if ID-based existence checks find the item
   - Verify if rename tracking arrays are populated
   - Confirm if cleanup removes the old version

3. **Trace Book Import Flow**:
   - Export a book with notes and folders
   - Examine the backup directory structure
   - Import to a new device
   - Verify book folder placement and cover import

4. **Check Sync Manifest**:
   - Examine manifest structure after operations
   - Verify ID formats are consistent
   - Check if relationships are properly maintained

This investigation plan provides a structured approach to diagnose the sync/backup issues by examining the relevant code components, potential inconsistencies, key functions, and architectural considerations.

---




















# DELETION TRACKING INVESTIGATION RESULTS:

## Current State Analysis

### 1. Sync System Deletion Tracking Implementation

**Status: ✅ IMPLEMENTED AND FUNCTIONAL**

The sync system has a comprehensive deletion tracking mechanism that operates differently from the old backup system:

#### Core Components:

1. **Manifest-Based Deletion Tracking** (`manifest-manager.ts`)
   - Deletions are tracked in the sync manifest under `manifest.deletions[]`
   - Each deletion record contains: `id`, `type`, `deletedAt`, `path`
   - When an item is removed, it's moved from `manifest.items` to `manifest.deletions`

2. **Change Detection System** (`change-detector.ts`)
   - `processDeletions()` method processes deletion records from manifest
   - `markForDeletion()` method collects items that need to be deleted locally
   - Returns `toDelete` array in the sync changes result

3. **Database Integration**
   - Deletion events are tracked through database hooks in API files
   - `notifyBookChange('delete', ...)`, `notifyFolderChange('delete', ...)`, `notifyNoteChange('delete', ...)`
   - These hooks trigger sync manifest updates

### 2. Key Findings

#### ✅ **Deletion Detection Works**
- The sync system properly detects when items are deleted on one device
- Deletions are recorded in the sync manifest with timestamps
- Remote deletions are identified during sync comparison

#### ✅ **Deletion Processing Exists**
- `processDeletions()` in `change-detector.ts` (lines 214-233) processes deletion records
- Items marked for deletion are collected in `pendingDeletions` array
- The system checks if deleted items still exist locally before marking them

#### ⚠️ **Deletion Execution Gap**
- `markForDeletion()` only adds items to a pending array but doesn't execute deletions
- The actual deletion of local database items is not implemented in the sync flow
- This means deletions from other devices don't propagate to the local database

### 3. Technical Implementation Details

**Deletion Flow:**
1. Device A deletes an item → Database hooks trigger → Manifest updated with deletion record
2. Device B syncs → `processDeletions()` identifies the deletion → `markForDeletion()` called
3. **MISSING STEP:** Local database deletion is not executed

**Current `markForDeletion()` Implementation:**
```typescript
private markForDeletion(id: string, type: 'book' | 'folder' | 'note'): void {
  this.pendingDeletions.push({ id, type });
  console.log(`Item ${id} (${type}) marked for deletion`);
  // Missing: Actual deletion execution
}
```

### 4. Comparison with Old Backup System

The investigation plan referenced `backup-storage.ts` and `removeDeletedItems()`, but these were part of the **old backup system** that has been replaced by the current sync system. The old system used:
- File-based deletion tracking by scanning backup directories
- `removeDeletedItems()` to clean up backup files
- Directory-based change detection

The **current sync system** uses:
- Manifest-based deletion tracking
- Database-centric approach
- ID-based item identification

### 5. Conclusion

**Deletion tracking is implemented but incomplete.** The sync system successfully:
- ✅ Tracks deletions in manifests
- ✅ Detects remote deletions during sync
- ✅ Identifies items that should be deleted locally

**However, it fails to:**
- ❌ Execute actual database deletions for items deleted on other devices
- ❌ Complete the sync deletion workflow

This explains why deletions don't propagate between devices in the current sync system.                                                                             